import type { Transaction, TransactionData } from "../types";

import { useCallback } from "react";

import { useShallow } from "zustand/react/shallow";

import useTransactionsStore from "../store";

export function useTransactionActions(transaction?: Transaction) {
  const { setDialogOpen, setDialogMode, setCurrentTransaction, setInitialValues } = useTransactionsStore(
    useShallow((state) => ({
      setDialogOpen: state.setDialogOpen,
      setDialogMode: state.setDialogMode,
      setCurrentTransaction: state.setCurrentTransaction,
      setInitialValues: state.setInitialValues,
    }))
  );

  const createTransaction = useCallback(
    (initialValues?: Partial<TransactionData>) => {
      setDialogMode("create");
      setDialogOpen(true);
      setInitialValues(initialValues);
    },
    [setDialogOpen, setDialogMode, setInitialValues]
  );

  const editTransaction = useCallback(() => {
    setDialogMode("edit");
    setDialogOpen(true);
    setCurrentTransaction(transaction);
  }, [setDialogOpen, setDialogMode, setCurrentTransaction, transaction]);

  const deleteTransaction = useCallback(() => {
    setDialogMode("delete");
    setDialogOpen(true);
    setCurrentTransaction(transaction);
  }, [setDialogOpen, setDialogMode, setCurrentTransaction, transaction]);

  return { createTransaction, editTransaction, deleteTransaction };
}
