import type { Transaction } from "../types";

import {
  BanknoteArrowDownIcon,
  BanknoteArrowUpIcon,
  CircleQuestionMarkIcon,
  EditIcon,
  MoreHorizontalIcon,
  Trash2Icon,
} from "lucide-react";

import Box from "~/components/blocks/box";
import DefinitionBlock from "~/components/blocks/definition-block";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { getAccountName } from "~/features/accounts/utils";
import { useBaseCurrency } from "~/features/auth/hooks";
import { formatCurrency, formatDate } from "~/lib/formatters";
import { cn } from "~/lib/utils";

import { useTransactionActions } from "../hooks";
import TransactionIcon from "./transaction-icon";

interface Props {
  transaction: Transaction;
}

export default function TransactionDetailsOverview({ transaction }: Props) {
  const baseCurrency = useBaseCurrency();

  const { editTransaction, deleteTransaction } = useTransactionActions(transaction);

  const isExpense = transaction.transaction_type === "expense";
  const isIncome = transaction.transaction_type === "income";
  const isTransfer = transaction.transaction_type === "transfer";

  return (
    <Box className="flex flex-col gap-6 ps-8 pe-4 pt-4 pb-8">
      <div className="flex items-center gap-4">
        <TransactionIcon transaction={transaction} className="size-4" />

        <p className="text-foreground text-xs/5 font-semibold uppercase" title="Created at">
          {formatDate(transaction.transaction_date)}
        </p>
        {isExpense && (
          <p className="text-red flex items-center gap-1 text-sm/5">
            <BanknoteArrowDownIcon className="size-4" />
            Expense
          </p>
        )}
        {isIncome && (
          <p className="text-green flex items-center gap-1 text-sm/5">
            <BanknoteArrowUpIcon className="size-4" />
            Income
          </p>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger className="ms-auto">
            <MoreHorizontalIcon className="text-gray hover:bg-gray/5 size-5 cursor-pointer rounded-xs" />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={editTransaction}>
              <EditIcon /> <span className="inline-block pt-0.5">Edit transaction</span>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={deleteTransaction} variant="destructive">
              <Trash2Icon /> <span className="inline-block pt-0.5">Delete</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      <div>
        <h2 className="text-foreground text-3xl/8 font-bold">
          {transaction.description ||
            (isTransfer ? "Transfer between accounts" : `Transaction on ${formatDate(transaction.transaction_date)}`)}
        </h2>
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title={isTransfer ? "Amount" : isExpense ? "Amount spent" : "Amount received"}>
          <p className="flex items-baseline gap-2">
            <span
              className={cn({
                "text-green": isIncome,
                "text-red": isExpense,
                "text-primary": isTransfer,
              })}
            >
              {formatCurrency(transaction.account.currency, transaction.amount)}
            </span>
            {transaction.account.currency !== baseCurrency && !isTransfer && (
              <span className="text-gray text-base font-normal">
                ({formatCurrency(baseCurrency, transaction.base_amount)})
              </span>
            )}
          </p>
        </DefinitionBlock>

        {isTransfer && (
          <DefinitionBlock title="Destination amount">
            <div className="flex items-baseline gap-2">
              <p
                className={cn({
                  "text-green": isIncome,
                  "text-red": isExpense,
                  "text-primary": isTransfer,
                })}
              >
                {formatCurrency(transaction.account_to!.currency, transaction.amount_to)}
              </p>

              {transaction.account.currency !== transaction.account_to!.currency && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="place-self-start">
                      <CircleQuestionMarkIcon />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48">
                    <DefinitionBlock title="Currency rate" size="sm">
                      <div>
                        <p>
                          {`1 ${transaction.account.currency} = ${(parseFloat(transaction.amount_to) / parseFloat(transaction.amount)).toFixed(4)} ${transaction.account_to!.currency}`}
                        </p>

                        <p>
                          {`1 ${transaction.account_to!.currency} = ${(parseFloat(transaction.amount) / parseFloat(transaction.amount_to)).toFixed(4)} ${transaction.account.currency}`}
                        </p>
                      </div>
                    </DefinitionBlock>
                  </PopoverContent>
                </Popover>
              )}
            </div>
          </DefinitionBlock>
        )}
      </div>

      <div className="flex gap-12">
        <DefinitionBlock title="Account">
          <p>{getAccountName(transaction.account)}</p>
        </DefinitionBlock>

        {isTransfer && (
          <DefinitionBlock title="Destination account">
            <p>{getAccountName(transaction.account_to!)}</p>
          </DefinitionBlock>
        )}
      </div>

      <div className="flex gap-4">
        {transaction.category && (
          <div className="w-fit border-l-4" style={{ borderColor: transaction.category.color }}>
            <p className="bg-background rounded-r-md py-2 ps-2.5 pe-4 text-xs/4 font-semibold uppercase">
              <span className="inline-block pt-0.5">{transaction.category.name}</span>
            </p>
          </div>
        )}
        <Badge size="lg" className="bg-primary/90 px-4 py-2 text-sm/4">
          {isTransfer ? "Transfer" : isExpense ? "Expense" : "Income"}
        </Badge>
      </div>
    </Box>
  );
}
